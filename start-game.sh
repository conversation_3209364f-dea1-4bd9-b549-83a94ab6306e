#!/bin/bash

echo "启动弹跳冒险家游戏服务器..."
echo ""

# 检查Python是否安装
if command -v python3 &> /dev/null; then
    echo "使用Python3启动HTTP服务器..."
    cd bounce-adventurer
    echo "服务器地址: http://localhost:8000"
    echo "按 Ctrl+C 停止服务器"
    echo ""
    
    # 自动打开浏览器（Mac）
    if [[ "$OSTYPE" == "darwin"* ]]; then
        open http://localhost:8000/index.html
    # 自动打开浏览器（Linux）
    elif [[ "$OSTYPE" == "linux-gnu"* ]]; then
        xdg-open http://localhost:8000/index.html
    fi
    
    python3 -m http.server 8000
elif command -v python &> /dev/null; then
    echo "使用Python启动HTTP服务器..."
    cd bounce-adventurer
    echo "服务器地址: http://localhost:8000"
    echo "按 Ctrl+C 停止服务器"
    echo ""
    
    # 自动打开浏览器（Mac）
    if [[ "$OSTYPE" == "darwin"* ]]; then
        open http://localhost:8000/index.html
    # 自动打开浏览器（Linux）
    elif [[ "$OSTYPE" == "linux-gnu"* ]]; then
        xdg-open http://localhost:8000/index.html
    fi
    
    python -m http.server 8000
else
    echo "错误：未找到Python，请安装Python或使用其他方法启动服务器"
    exit 1
fi
