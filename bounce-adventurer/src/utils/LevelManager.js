/**
 * 关卡管理器
 * 负责管理游戏关卡的加载、进度保存等功能
 */
class LevelManager {
    constructor() {
        this.currentLevel = 1;
        this.maxLevel = 1; // 当前可玩的最高关卡
        this.levelData = {};
        this.playerProgress = this.loadProgress();
    }
    
    /**
     * 加载关卡数据
     * @param {number} levelNumber 关卡编号
     * @param {object} data 关卡数据
     */
    loadLevelData(levelNumber, data) {
        this.levelData[levelNumber] = data;
    }
    
    /**
     * 获取关卡数据
     * @param {number} levelNumber 关卡编号
     * @returns {object} 关卡数据
     */
    getLevelData(levelNumber) {
        return this.levelData[levelNumber] || null;
    }
    
    /**
     * 设置当前关卡
     * @param {number} levelNumber 关卡编号
     */
    setCurrentLevel(levelNumber) {
        if (levelNumber <= this.maxLevel) {
            this.currentLevel = levelNumber;
        }
    }
    
    /**
     * 获取当前关卡
     * @returns {number} 当前关卡编号
     */
    getCurrentLevel() {
        return this.currentLevel;
    }
    
    /**
     * 获取最高可玩关卡
     * @returns {number} 最高关卡编号
     */
    getMaxLevel() {
        return this.maxLevel;
    }
    
    /**
     * 完成关卡
     * @param {number} levelNumber 关卡编号
     * @param {number} stars 获得的星数
     * @param {number} score 得分
     */
    completeLevel(levelNumber, stars, score) {
        // 更新玩家进度
        if (!this.playerProgress.levels[levelNumber]) {
            this.playerProgress.levels[levelNumber] = {};
        }
        
        const levelProgress = this.playerProgress.levels[levelNumber];
        
        // 更新最高星数
        if (!levelProgress.stars || stars > levelProgress.stars) {
            levelProgress.stars = stars;
        }
        
        // 更新最高分数
        if (!levelProgress.bestScore || score > levelProgress.bestScore) {
            levelProgress.bestScore = score;
        }
        
        // 解锁下一关
        if (levelNumber === this.maxLevel && levelNumber < this.getTotalLevels()) {
            this.maxLevel = levelNumber + 1;
            this.playerProgress.maxLevel = this.maxLevel;
        }
        
        // 保存进度
        this.saveProgress();
    }
    
    /**
     * 获取关卡进度
     * @param {number} levelNumber 关卡编号
     * @returns {object} 关卡进度信息
     */
    getLevelProgress(levelNumber) {
        return this.playerProgress.levels[levelNumber] || {
            stars: 0,
            bestScore: 0,
            completed: false
        };
    }
    
    /**
     * 获取总关卡数
     * @returns {number} 总关卡数
     */
    getTotalLevels() {
        return Object.keys(this.levelData).length || 10; // 默认10关
    }
    
    /**
     * 检查关卡是否解锁
     * @param {number} levelNumber 关卡编号
     * @returns {boolean} 是否解锁
     */
    isLevelUnlocked(levelNumber) {
        return levelNumber <= this.maxLevel;
    }
    
    /**
     * 重置游戏进度
     */
    resetProgress() {
        this.playerProgress = {
            maxLevel: 1,
            levels: {}
        };
        this.maxLevel = 1;
        this.currentLevel = 1;
        this.saveProgress();
    }
    
    /**
     * 从本地存储加载进度
     * @returns {object} 玩家进度数据
     */
    loadProgress() {
        try {
            const saved = localStorage.getItem('bounceAdventurer_progress');
            if (saved) {
                const progress = JSON.parse(saved);
                this.maxLevel = progress.maxLevel || 1;
                return progress;
            }
        } catch (error) {
            console.warn('加载游戏进度失败:', error);
        }
        
        // 返回默认进度
        return {
            maxLevel: 1,
            levels: {}
        };
    }
    
    /**
     * 保存进度到本地存储
     */
    saveProgress() {
        try {
            localStorage.setItem('bounceAdventurer_progress', JSON.stringify(this.playerProgress));
        } catch (error) {
            console.warn('保存游戏进度失败:', error);
        }
    }
    
    /**
     * 获取下一关编号
     * @returns {number|null} 下一关编号，如果没有则返回null
     */
    getNextLevel() {
        const nextLevel = this.currentLevel + 1;
        return nextLevel <= this.getTotalLevels() ? nextLevel : null;
    }
    
    /**
     * 获取上一关编号
     * @returns {number|null} 上一关编号，如果没有则返回null
     */
    getPreviousLevel() {
        const prevLevel = this.currentLevel - 1;
        return prevLevel >= 1 ? prevLevel : null;
    }
    
    /**
     * 计算总星数
     * @returns {number} 总星数
     */
    getTotalStars() {
        let totalStars = 0;
        for (const levelNumber in this.playerProgress.levels) {
            const levelProgress = this.playerProgress.levels[levelNumber];
            totalStars += levelProgress.stars || 0;
        }
        return totalStars;
    }
    
    /**
     * 计算总分数
     * @returns {number} 总分数
     */
    getTotalScore() {
        let totalScore = 0;
        for (const levelNumber in this.playerProgress.levels) {
            const levelProgress = this.playerProgress.levels[levelNumber];
            totalScore += levelProgress.bestScore || 0;
        }
        return totalScore;
    }
}

// 创建全局实例
window.LevelManager = new LevelManager();
