class GameScene extends Phaser.Scene {
    constructor() {
        super({ key: 'GameScene' });
        this.player = null;
        this.platforms = null;
        this.gems = null;
        this.bouncePlatforms = null;
        this.movingPlatforms = null;
        this.spikes = null;
        this.portals = null;
        this.windAreas = null;
        this.extraBounces = null;
        this.goal = null;
        
        this.isAiming = false;
        this.aimLine = null;
        this.bounceCount = 0;
        this.gemCount = 0;
        this.totalGems = 0;
        
        this.bounceText = null;
        this.gemText = null;
        this.levelText = null;
    }

    create() {
        // 设置世界边界
        this.physics.world.setBounds(0, 0, 800, 600);
        
        // 添加背景
        this.add.image(400, 300, 'background');
        
        // 创建游戏对象组
        this.platforms = this.physics.add.staticGroup();
        this.gems = this.physics.add.group();
        this.bouncePlatforms = this.physics.add.staticGroup();
        this.movingPlatforms = this.physics.add.group();
        this.spikes = this.physics.add.staticGroup();
        this.portals = this.physics.add.staticGroup();
        this.windAreas = this.physics.add.group();
        this.extraBounces = this.physics.add.group();
        
        // 加载关卡
        this.loadLevel(this.game.gameData.currentLevel);
        
        // 创建玩家
        this.player = this.physics.add.sprite(100, 450, 'player');
        this.player.setBounce(0.2);
        this.player.setCollideWorldBounds(true);
        this.player.body.setCircle(28, 4, 4);
        this.player.play('idle');
        
        // 设置碰撞
        this.physics.add.collider(this.player, this.platforms);
        this.physics.add.collider(this.player, this.bouncePlatforms, this.bounceHigher, null, this);
        this.physics.add.collider(this.player, this.movingPlatforms);
        
        // 设置重叠检测
        this.physics.add.overlap(this.player, this.gems, this.collectGem, null, this);
        this.physics.add.overlap(this.player, this.spikes, this.hitSpike, null, this);
        this.physics.add.overlap(this.player, this.portals, this.usePortal, null, this);
        this.physics.add.overlap(this.player, this.windAreas, this.applyWind, null, this);
        this.physics.add.overlap(this.player, this.extraBounces, this.collectExtraBounce, null, this);
        this.physics.add.overlap(this.player, this.goal, this.reachGoal, null, this);
        
        // 创建瞄准线
        this.aimLine = this.add.graphics();
        
        // 设置输入事件
        this.input.on('pointerdown', this.startAiming, this);
        this.input.on('pointermove', this.updateAiming, this);
        this.input.on('pointerup', this.launchPlayer, this);
        
        // 创建UI
        this.createUI();
        
        // 添加相机跟随
        this.cameras.main.startFollow(this.player, true, 0.1, 0.1);
        this.cameras.main.setZoom(1);
        
        // 添加淡入效果
        this.cameras.main.fadeIn(500);
    }
    
    update() {
        // 更新移动平台
        this.movingPlatforms.children.iterate(function(platform) {
            if (platform.moveRight) {
                platform.x += platform.speed;
                if (platform.x >= platform.startX + platform.distance) {
                    platform.moveRight = false;
                }
            } else {
                platform.x -= platform.speed;
                if (platform.x <= platform.startX) {
                    platform.moveRight = true;
                }
            }
        });
        
        // 检查玩家是否静止
        if (this.player.body.velocity.x < 10 && this.player.body.velocity.x > -10 &&
            this.player.body.velocity.y < 10 && this.player.body.velocity.y > -10 &&
            this.player.body.touching.down) {
            this.player.play('idle', true);
        }
        
        // 检查是否用完弹跳次数且玩家静止
        if (this.bounceCount >= this.game.gameData.maxBounces && 
            this.player.body.velocity.x < 10 && this.player.body.velocity.x > -10 &&
            this.player.body.velocity.y < 10 && this.player.body.velocity.y > -10) {
            this.gameOver(false);
        }
    }
    
    loadLevel(levelNumber) {
        // 获取关卡数据
        const levelData = this.cache.json.get('level1'); // 暂时使用level1
        
        // 清除现有对象
        this.platforms.clear(true, true);
        this.gems.clear(true, true);
        this.bouncePlatforms.clear(true, true);
        this.movingPlatforms.clear(true, true);
        this.spikes.clear(true, true);
        this.portals.clear(true, true);
        this.windAreas.clear(true, true);
        this.extraBounces.clear(true, true);
        
        // 创建平台
        levelData.platforms.forEach(platform => {
            this.platforms.create(platform.x, platform.y, 'platform')
                .setScale(platform.width / 100, platform.height / 20)
                .refreshBody();
        });
        
        // 创建弹力平台
        levelData.bouncePlatforms.forEach(platform => {
            this.bouncePlatforms.create(platform.x, platform.y, 'bounce-platform')
                .setScale(platform.width / 100, platform.height / 20)
                .refreshBody();
        });
        
        // 创建移动平台
        levelData.movingPlatforms.forEach(platform => {
            const movingPlatform = this.movingPlatforms.create(platform.x, platform.y, 'moving-platform')
                .setScale(platform.width / 100, platform.height / 20);
            movingPlatform.startX = platform.x;
            movingPlatform.distance = platform.distance;
            movingPlatform.speed = platform.speed;
            movingPlatform.moveRight = true;
            movingPlatform.body.immovable = true;
        });
        
        // 创建宝石
        levelData.gems.forEach(gem => {
            this.gems.create(gem.x, gem.y, 'gem');
        });
        this.totalGems = levelData.gems.length;
        
        // 创建尖刺
        levelData.spikes.forEach(spike => {
            this.spikes.create(spike.x, spike.y, 'spike');
        });
        
        // 创建传送门
        levelData.portals.forEach(portal => {
            const portalObj = this.portals.create(portal.x, portal.y, 'portal');
            portalObj.targetX = portal.targetX;
            portalObj.targetY = portal.targetY;
        });
        
        // 创建风区
        levelData.windAreas.forEach(wind => {
            const windObj = this.windAreas.create(wind.x, wind.y, 'wind')
                .setScale(wind.width / 100, wind.height / 100)
                .setAlpha(0.5);
            windObj.forceX = wind.forceX;
            windObj.forceY = wind.forceY;
            windObj.body.immovable = true;
        });
        
        // 创建额外弹跳道具
        levelData.extraBounces.forEach(bounce => {
            this.extraBounces.create(bounce.x, bounce.y, 'extra-bounce');
        });
        
        // 创建终点
        this.goal = this.physics.add.sprite(levelData.goal.x, levelData.goal.y, 'platform')
            .setScale(0.5)
            .setTint(0x00ff00);
        this.goal.body.immovable = true;
        
        // 重置计数器
        this.bounceCount = 0;
        this.gemCount = 0;
        
        // 设置最大弹跳次数
        this.game.gameData.maxBounces = levelData.maxBounces;
    }
    
    createUI() {
        // 创建弹跳次数显示
        this.bounceText = this.add.text(20, 20, '弹跳: 0/' + this.game.gameData.maxBounces, {
            fontFamily: 'Arial',
            fontSize: '24px',
            color: '#ffffff',
            stroke: '#000000',
            strokeThickness: 4
        }).setScrollFactor(0);
        
        // 创建宝石计数显示
        this.gemText = this.add.text(20, 60, '宝石: 0/' + this.totalGems, {
            fontFamily: 'Arial',
            fontSize: '24px',
            color: '#ffffff',
            stroke: '#000000',
            strokeThickness: 4
        }).setScrollFactor(0);
        
        // 创建关卡显示
        this.levelText = this.add.text(20, 100, '关卡: ' + this.game.gameData.currentLevel, {
            fontFamily: 'Arial',
            fontSize: '24px',
            color: '#ffffff',
            stroke: '#000000',
            strokeThickness: 4
        }).setScrollFactor(0);
        
        // 创建重新开始按钮
        const restartButton = this.add.text(750, 20, '重新开始', {
            fontFamily: 'Arial',
            fontSize: '20px',
            color: '#ffffff',
            backgroundColor: '#ff0000',
            padding: { left: 10, right: 10, top: 5, bottom: 5 },
            stroke: '#000000',
            strokeThickness: 2
        }).setOrigin(1, 0).setScrollFactor(0).setInteractive({ useHandCursor: true });
        
        restartButton.on('pointerdown', () => {
            this.scene.restart();
        });
    }
    
    startAiming(pointer) {
        // 只有当玩家静止且弹跳次数未用完时才能瞄准
        if (this.player.body.velocity.x < 10 && this.player.body.velocity.x > -10 &&
            this.player.body.velocity.y < 10 && this.player.body.velocity.y > -10 &&
            this.bounceCount < this.game.gameData.maxBounces) {
            this.isAiming = true;
        }
    }
    
    updateAiming(pointer) {
        if (this.isAiming) {
            // 计算方向和力度
            const angle = Phaser.Math.Angle.Between(this.player.x, this.player.y, pointer.worldX, pointer.worldY);
            const distance = Phaser.Math.Distance.Between(this.player.x, this.player.y, pointer.worldX, pointer.worldY);
            const power = Math.min(distance / 3, 200); // 限制最大力度
            
            // 绘制瞄准线
            this.aimLine.clear();
            this.aimLine.lineStyle(2, 0xffffff);
            this.aimLine.beginPath();
            this.aimLine.moveTo(this.player.x, this.player.y);
            this.aimLine.lineTo(
                this.player.x + Math.cos(angle) * power,
                this.player.y + Math.sin(angle) * power
            );
            this.aimLine.strokePath();
        }
    }
    
    launchPlayer(pointer) {
        if (this.isAiming) {
            // 计算方向和力度
            const angle = Phaser.Math.Angle.Between(this.player.x, this.player.y, pointer.worldX, pointer.worldY);
            const distance = Phaser.Math.Distance.Between(this.player.x, this.player.y, pointer.worldX, pointer.worldY);
            const power = Math.min(distance / 3, 200); // 限制最大力度
            
            // 应用力量
            this.player.setVelocity(
                Math.cos(angle) * power,
                Math.sin(angle) * power
            );
            
            // 播放弹跳动画和音效
            this.player.play('bounce');
            this.sound.play('bounce');
            
            // 增加弹跳计数
            this.bounceCount++;
            this.bounceText.setText('弹跳: ' + this.bounceCount + '/' + this.game.gameData.maxBounces);
            
            // 清除瞄准线
            this.aimLine.clear();
            this.isAiming = false;
        }
    }
    
    collectGem(player, gem) {
        // 播放收集动画和音效
        gem.disableBody(true, true);
        this.sound.play('collect');
        
        // 增加宝石计数
        this.gemCount++;
        this.gemText.setText('宝石: ' + this.gemCount + '/' + this.totalGems);
        
        // 播放收集动画
        player.play('collect');
    }
    
    bounceHigher(player, platform) {
        // 在弹力平台上弹跳更高
        player.setVelocityY(-400);
        this.sound.play('bounce');
        player.play('bounce');
    }
    
    hitSpike(player, spike) {
        // 碰到尖刺增加弹跳次数惩罚
        this.bounceCount += 2;
        this.bounceText.setText('弹跳: ' + this.bounceCount + '/' + this.game.gameData.maxBounces);
        
        // 播放受伤效果
        player.setTint(0xff0000);
        this.time.delayedCall(300, () => {
            player.clearTint();
        });
        
        // 检查是否用完弹跳次数
        if (this.bounceCount >= this.game.gameData.maxBounces) {
            this.gameOver(false);
        }
    }
    
    usePortal(player, portal) {
        // 传送玩家
        player.x = portal.targetX;
        player.y = portal.targetY;
        
        // 播放传送效果
        this.cameras.main.flash(500, 0, 0, 0);
    }
    
    applyWind(player, wind) {
        // 应用风力
        player.body.velocity.x += wind.forceX;
        player.body.velocity.y += wind.forceY;
    }
    
    collectExtraBounce(player, extraBounce) {
        // 收集额外弹跳次数
        extraBounce.disableBody(true, true);
        this.sound.play('collect');
        
        // 减少弹跳计数
        this.bounceCount = Math.max(0, this.bounceCount - 1);
        this.bounceText.setText('弹跳: ' + this.bounceCount + '/' + this.game.gameData.maxBounces);
    }
    
    reachGoal(player, goal) {
        // 到达终点
        this.gameOver(true);
    }
    
    gameOver(success) {
        // 停止物理
        this.physics.pause();
        
        // 保存游戏数据
        this.game.gameData.currentLevelGems = this.gemCount;
        this.game.gameData.totalGems += this.gemCount;
        this.game.gameData.bounceCount = this.bounceCount;
        
        // 计算星级
        let stars = 0;
        if (success) {
            // 基于收集的宝石和使用的弹跳次数计算星级
            const gemRatio = this.gemCount / this.totalGems;
            const bounceRatio = this.bounceCount / this.game.gameData.maxBounces;
            
            if (gemRatio >= 0.9 && bounceRatio <= 0.7) {
                stars = 3;
            } else if (gemRatio >= 0.6 && bounceRatio <= 0.9) {
                stars = 2;
            } else {
                stars = 1;
            }
        }
        this.game.gameData.stars = stars;
        
        // 播放成功或失败音效
        if (success) {
            this.sound.play('success');
        } else {
            this.sound.play('fail');
        }
        
        // 淡出并切换到结果场景
        this.cameras.main.fade(1000, 0, 0, 0);
        this.time.delayedCall(1000, () => {
            this.scene.start('ResultScene', { success: success });
        });
    }
}
