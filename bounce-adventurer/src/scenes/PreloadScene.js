class PreloadScene extends Phaser.Scene {
    constructor() {
        super({ key: 'PreloadScene' });
    }

    preload() {
        // 创建加载界面
        this.createLoadingBar();

        // 加载游戏资源
        this.loadGameAssets();
    }

    createLoadingBar() {
        // 创建简单的加载进度条
        const width = this.cameras.main.width;
        const height = this.cameras.main.height;
        
        const progressBar = this.add.graphics();
        const progressBox = this.add.graphics();
        progressBox.fillStyle(0x222222, 0.8);
        progressBox.fillRect(width / 2 - 160, height / 2 - 25, 320, 50);
        
        const loadingText = this.make.text({
            x: width / 2,
            y: height / 2 - 50,
            text: '加载中...',
            style: {
                font: '20px monospace',
                fill: '#ffffff'
            }
        });
        loadingText.setOrigin(0.5, 0.5);
        
        const percentText = this.make.text({
            x: width / 2,
            y: height / 2,
            text: '0%',
            style: {
                font: '18px monospace',
                fill: '#ffffff'
            }
        });
        percentText.setOrigin(0.5, 0.5);
        
        // 更新加载进度
        this.load.on('progress', (value) => {
            percentText.setText(parseInt(value * 100) + '%');
            progressBar.clear();
            progressBar.fillStyle(0xffffff, 1);
            progressBar.fillRect(width / 2 - 150, height / 2 - 15, 300 * value, 30);
        });
        
        this.load.on('complete', () => {
            progressBar.destroy();
            progressBox.destroy();
            loadingText.destroy();
            percentText.destroy();
        });
    }

    loadGameAssets() {
        // 加载图像资源
        this.load.image('background', 'assets/images/background.png');
        this.load.image('platform', 'assets/images/platform.png');
        this.load.image('bounce-platform', 'assets/images/bounce-platform.png');
        this.load.image('moving-platform', 'assets/images/moving-platform.png');
        this.load.image('gem', 'assets/images/gem.png');
        this.load.image('spike', 'assets/images/spike.png');
        this.load.image('portal', 'assets/images/portal.png');
        this.load.image('wind', 'assets/images/wind.png');
        this.load.image('extra-bounce', 'assets/images/extra-bounce.png');
        this.load.image('star', 'assets/images/star.png');
        this.load.image('empty-star', 'assets/images/empty-star.png');
        
        // 加载精灵图
        this.load.spritesheet('player', 'assets/images/player.png', { 
            frameWidth: 64, 
            frameHeight: 64 
        });
        
        // 加载音频资源
        this.load.audio('bounce', 'assets/audio/bounce.wav');
        this.load.audio('collect', 'assets/audio/collect.wav');
        this.load.audio('success', 'assets/audio/success.wav');
        this.load.audio('fail', 'assets/audio/fail.wav');
        this.load.audio('background-music', 'assets/audio/background-music.wav');
        
        // 加载关卡数据
        this.load.json('level1', 'assets/levels/level1.json');
    }

    create() {
        // 创建动画
        this.createAnimations();
        
        // 进入主菜单场景
        this.scene.start('MenuScene');
    }

    createAnimations() {
        // 创建玩家动画
        this.anims.create({
            key: 'idle',
            frames: this.anims.generateFrameNumbers('player', { start: 0, end: 3 }),
            frameRate: 8,
            repeat: -1
        });
        
        this.anims.create({
            key: 'bounce',
            frames: this.anims.generateFrameNumbers('player', { start: 4, end: 7 }),
            frameRate: 10,
            repeat: 0
        });
        
        this.anims.create({
            key: 'collect',
            frames: this.anims.generateFrameNumbers('player', { start: 8, end: 11 }),
            frameRate: 10,
            repeat: 0
        });
    }
}
