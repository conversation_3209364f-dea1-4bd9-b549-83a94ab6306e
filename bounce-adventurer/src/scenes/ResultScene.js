class ResultScene extends Phaser.Scene {
    constructor() {
        super({ key: 'ResultScene' });
    }

    init(data) {
        this.success = data.success;
    }

    create() {
        // 添加背景
        this.add.image(400, 300, 'background').setAlpha(0.5);
        
        // 添加结果标题
        const titleText = this.success ? '关卡完成！' : '挑战失败';
        const titleColor = this.success ? '#4CAF50' : '#F44336';
        
        this.add.text(400, 150, titleText, {
            fontFamily: 'Arial',
            fontSize: '64px',
            color: titleColor,
            stroke: '#000000',
            strokeThickness: 6,
            shadow: { offsetX: 2, offsetY: 2, color: '#000000', blur: 2, stroke: true, fill: true }
        }).setOrigin(0.5);
        
        // 如果成功，显示星级评价
        if (this.success) {
            // 显示星级
            const stars = this.game.gameData.stars;
            const starY = 230;
            
            for (let i = 0; i < 3; i++) {
                const x = 400 + (i - 1) * 80;
                const texture = i < stars ? 'star' : 'empty-star';
                this.add.image(x, starY, texture).setScale(0.8);
            }
            
            // 显示收集的宝石数量
            this.add.text(400, 300, `收集宝石: ${this.game.gameData.currentLevelGems}/${this.getTotalGems()}`, {
                fontFamily: 'Arial',
                fontSize: '28px',
                color: '#ffffff',
                stroke: '#000000',
                strokeThickness: 4
            }).setOrigin(0.5);
            
            // 显示使用的弹跳次数
            this.add.text(400, 340, `使用弹跳: ${this.game.gameData.bounceCount}/${this.game.gameData.maxBounces}`, {
                fontFamily: 'Arial',
                fontSize: '28px',
                color: '#ffffff',
                stroke: '#000000',
                strokeThickness: 4
            }).setOrigin(0.5);
            
            // 创建下一关按钮
            if (this.game.gameData.currentLevel < this.game.gameData.totalLevels) {
                const nextLevelButton = this.add.text(400, 420, '下一关', {
                    fontFamily: 'Arial',
                    fontSize: '32px',
                    color: '#ffffff',
                    backgroundColor: '#4CAF50',
                    padding: { left: 20, right: 20, top: 10, bottom: 10 },
                    stroke: '#000000',
                    strokeThickness: 2
                }).setOrigin(0.5).setInteractive({ useHandCursor: true });
                
                nextLevelButton.on('pointerover', () => {
                    nextLevelButton.setStyle({ backgroundColor: '#66BB6A' });
                });
                
                nextLevelButton.on('pointerout', () => {
                    nextLevelButton.setStyle({ backgroundColor: '#4CAF50' });
                });
                
                nextLevelButton.on('pointerdown', () => {
                    this.game.gameData.currentLevel++;
                    this.scene.start('GameScene');
                });
            } else {
                // 如果是最后一关，显示完成游戏消息
                this.add.text(400, 420, '恭喜！全部关卡完成！', {
                    fontFamily: 'Arial',
                    fontSize: '32px',
                    color: '#FFD700',
                    stroke: '#000000',
                    strokeThickness: 4
                }).setOrigin(0.5);
            }
        } else {
            // 失败信息
            this.add.text(400, 250, '弹跳次数用尽', {
                fontFamily: 'Arial',
                fontSize: '28px',
                color: '#ffffff',
                stroke: '#000000',
                strokeThickness: 4
            }).setOrigin(0.5);
            
            // 显示收集的宝石数量
            this.add.text(400, 300, `收集宝石: ${this.game.gameData.currentLevelGems}/${this.getTotalGems()}`, {
                fontFamily: 'Arial',
                fontSize: '28px',
                color: '#ffffff',
                stroke: '#000000',
                strokeThickness: 4
            }).setOrigin(0.5);
        }
        
        // 创建重试按钮
        const retryButton = this.add.text(400, this.success ? 490 : 420, '重新挑战', {
            fontFamily: 'Arial',
            fontSize: '32px',
            color: '#ffffff',
            backgroundColor: '#2196F3',
            padding: { left: 20, right: 20, top: 10, bottom: 10 },
            stroke: '#000000',
            strokeThickness: 2
        }).setOrigin(0.5).setInteractive({ useHandCursor: true });
        
        retryButton.on('pointerover', () => {
            retryButton.setStyle({ backgroundColor: '#42A5F5' });
        });
        
        retryButton.on('pointerout', () => {
            retryButton.setStyle({ backgroundColor: '#2196F3' });
        });
        
        retryButton.on('pointerdown', () => {
            this.scene.start('GameScene');
        });
        
        // 创建返回主菜单按钮
        const menuButton = this.add.text(400, this.success ? 560 : 490, '返回主菜单', {
            fontFamily: 'Arial',
            fontSize: '28px',
            color: '#ffffff',
            backgroundColor: '#FF9800',
            padding: { left: 20, right: 20, top: 8, bottom: 8 },
            stroke: '#000000',
            strokeThickness: 2
        }).setOrigin(0.5).setInteractive({ useHandCursor: true });
        
        menuButton.on('pointerover', () => {
            menuButton.setStyle({ backgroundColor: '#FFA726' });
        });
        
        menuButton.on('pointerout', () => {
            menuButton.setStyle({ backgroundColor: '#FF9800' });
        });
        
        menuButton.on('pointerdown', () => {
            this.scene.start('MenuScene');
        });
        
        // 添加广告按钮（示例）
        if (!this.success) {
            const adButton = this.add.text(400, 360, '观看广告获得额外弹跳次数', {
                fontFamily: 'Arial',
                fontSize: '24px',
                color: '#ffffff',
                backgroundColor: '#9C27B0',
                padding: { left: 20, right: 20, top: 8, bottom: 8 },
                stroke: '#000000',
                strokeThickness: 2
            }).setOrigin(0.5).setInteractive({ useHandCursor: true });
            
            adButton.on('pointerover', () => {
                adButton.setStyle({ backgroundColor: '#BA68C8' });
            });
            
            adButton.on('pointerout', () => {
                adButton.setStyle({ backgroundColor: '#9C27B0' });
            });
            
            adButton.on('pointerdown', () => {
                this.showMessage('广告功能将在正式版中启用！');
            });
        }
        
        // 添加分享按钮
        const shareButton = this.add.text(700, 550, '分享', {
            fontFamily: 'Arial',
            fontSize: '24px',
            color: '#ffffff',
            backgroundColor: '#00BCD4',
            padding: { left: 15, right: 15, top: 8, bottom: 8 },
            stroke: '#000000',
            strokeThickness: 2
        }).setOrigin(0.5).setInteractive({ useHandCursor: true });
        
        shareButton.on('pointerover', () => {
            shareButton.setStyle({ backgroundColor: '#4DD0E1' });
        });
        
        shareButton.on('pointerout', () => {
            shareButton.setStyle({ backgroundColor: '#00BCD4' });
        });
        
        shareButton.on('pointerdown', () => {
            this.showMessage('分享功能将在正式版中启用！');
        });
        
        // 添加淡入效果
        this.cameras.main.fadeIn(1000);
    }
    
    getTotalGems() {
        // 这里应该从关卡数据中获取总宝石数
        // 暂时返回一个固定值
        return 10;
    }
    
    showMessage(message) {
        // 创建消息背景
        const messageBox = this.add.graphics();
        messageBox.fillStyle(0x000000, 0.7);
        messageBox.fillRoundedRect(200, 250, 400, 100, 16);
        
        // 创建消息文本
        const messageText = this.add.text(400, 300, message, {
            fontFamily: 'Arial',
            fontSize: '24px',
            color: '#ffffff',
            align: 'center'
        }).setOrigin(0.5);
        
        // 2秒后自动消失
        this.time.delayedCall(2000, () => {
            messageBox.destroy();
            messageText.destroy();
        });
    }
}
