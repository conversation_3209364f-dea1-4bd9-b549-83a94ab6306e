class MenuScene extends Phaser.Scene {
    constructor() {
        super({ key: 'MenuScene' });
    }

    create() {
        // 添加背景
        this.add.image(400, 300, 'background').setScale(1.2);
        
        // 添加游戏标题
        const title = this.add.text(400, 150, '弹跳冒险家', {
            fontFamily: 'Arial',
            fontSize: '64px',
            color: '#ffffff',
            stroke: '#000000',
            strokeThickness: 6,
            shadow: { offsetX: 2, offsetY: 2, color: '#000000', blur: 2, stroke: true, fill: true }
        }).setOrigin(0.5);
        
        // 添加副标题
        this.add.text(400, 220, 'Bounce Adventurer', {
            fontFamily: 'Arial',
            fontSize: '32px',
            color: '#ffffff',
            stroke: '#000000',
            strokeThickness: 4
        }).setOrigin(0.5);
        
        // 创建开始游戏按钮
        const startButton = this.add.text(400, 350, '开始游戏', {
            fontFamily: 'Arial',
            fontSize: '36px',
            color: '#ffffff',
            backgroundColor: '#4CAF50',
            padding: { left: 30, right: 30, top: 10, bottom: 10 },
            stroke: '#000000',
            strokeThickness: 2
        }).setOrigin(0.5).setInteractive({ useHandCursor: true });
        
        // 添加按钮悬停效果
        startButton.on('pointerover', () => {
            startButton.setStyle({ backgroundColor: '#66BB6A' });
        });
        
        startButton.on('pointerout', () => {
            startButton.setStyle({ backgroundColor: '#4CAF50' });
        });
        
        // 添加按钮点击事件
        startButton.on('pointerdown', () => {
            this.cameras.main.fade(500, 0, 0, 0);
            this.time.delayedCall(500, () => {
                this.scene.start('GameScene');
            });
        });
        
        // 创建排行榜按钮
        const leaderboardButton = this.add.text(400, 430, '排行榜', {
            fontFamily: 'Arial',
            fontSize: '28px',
            color: '#ffffff',
            backgroundColor: '#2196F3',
            padding: { left: 20, right: 20, top: 8, bottom: 8 },
            stroke: '#000000',
            strokeThickness: 2
        }).setOrigin(0.5).setInteractive({ useHandCursor: true });
        
        // 添加按钮悬停效果
        leaderboardButton.on('pointerover', () => {
            leaderboardButton.setStyle({ backgroundColor: '#42A5F5' });
        });
        
        leaderboardButton.on('pointerout', () => {
            leaderboardButton.setStyle({ backgroundColor: '#2196F3' });
        });
        
        // 添加按钮点击事件（暂时只显示提示）
        leaderboardButton.on('pointerdown', () => {
            this.showMessage('排行榜功能即将上线！');
        });
        
        // 创建设置按钮
        const settingsButton = this.add.text(400, 490, '设置', {
            fontFamily: 'Arial',
            fontSize: '28px',
            color: '#ffffff',
            backgroundColor: '#FF9800',
            padding: { left: 20, right: 20, top: 8, bottom: 8 },
            stroke: '#000000',
            strokeThickness: 2
        }).setOrigin(0.5).setInteractive({ useHandCursor: true });
        
        // 添加按钮悬停效果
        settingsButton.on('pointerover', () => {
            settingsButton.setStyle({ backgroundColor: '#FFA726' });
        });
        
        settingsButton.on('pointerout', () => {
            settingsButton.setStyle({ backgroundColor: '#FF9800' });
        });
        
        // 添加按钮点击事件（暂时只显示提示）
        settingsButton.on('pointerdown', () => {
            this.showMessage('设置功能即将上线！');
        });
        
        // 添加版本信息
        this.add.text(780, 580, 'v0.1', {
            fontFamily: 'Arial',
            fontSize: '16px',
            color: '#ffffff'
        }).setOrigin(1);
        
        // 添加淡入效果
        this.cameras.main.fadeIn(1000);
        
        // 播放背景音乐（循环）
        if (!this.sound.get('background-music')) {
            this.sound.add('background-music', { loop: true, volume: 0.5 }).play();
        }
    }
    
    showMessage(message) {
        // 创建消息背景
        const messageBox = this.add.graphics();
        messageBox.fillStyle(0x000000, 0.7);
        messageBox.fillRoundedRect(200, 250, 400, 100, 16);
        
        // 创建消息文本
        const messageText = this.add.text(400, 300, message, {
            fontFamily: 'Arial',
            fontSize: '24px',
            color: '#ffffff',
            align: 'center'
        }).setOrigin(0.5);
        
        // 2秒后自动消失
        this.time.delayedCall(2000, () => {
            messageBox.destroy();
            messageText.destroy();
        });
    }
}
