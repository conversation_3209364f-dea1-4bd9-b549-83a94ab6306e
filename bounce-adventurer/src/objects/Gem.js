class Gem extends Phaser.Physics.Arcade.Sprite {
    constructor(scene, x, y) {
        super(scene, x, y, 'gem');
        
        // 添加到场景和物理系统
        scene.add.existing(this);
        scene.physics.add.existing(this, true); // true表示静态物体
        
        // 添加简单的动画效果
        this.scene = scene;
        this.startY = y;
        
        // 设置宝石轻微浮动动画
        scene.tweens.add({
            targets: this,
            y: y - 10,
            duration: 1500,
            ease: 'Sine.easeInOut',
            yoyo: true,
            repeat: -1
        });
        
        // 添加轻微旋转
        scene.tweens.add({
            targets: this,
            angle: 360,
            duration: 6000,
            ease: 'Linear',
            repeat: -1
        });
    }
    
    collect() {
        // 播放收集动画
        this.scene.tweens.add({
            targets: this,
            y: this.y - 50,
            alpha: 0,
            scale: 1.5,
            duration: 300,
            ease: 'Power2',
            onComplete: () => {
                this.destroy();
            }
        });
    }
}
