class Platform extends Phaser.Physics.Arcade.Sprite {
    constructor(scene, x, y, type = 'normal') {
        // 根据类型选择纹理
        const texture = type === 'bounce' ? 'bounce-platform' : 
                        type === 'moving' ? 'moving-platform' : 'platform';
        
        super(scene, x, y, texture);
        
        // 添加到场景和物理系统
        scene.add.existing(this);
        scene.physics.add.existing(this, true); // true表示静态物体
        
        // 设置类型
        this.platformType = type;
        
        // 如果是移动平台，设置为动态物体
        if (type === 'moving') {
            this.body.immovable = true;
            this.body.moves = true;
        }
    }
    
    setSize(width, height) {
        super.setSize(width, height);
        this.displayWidth = width;
        this.displayHeight = height;
        return this;
    }
    
    setupMovement(startX, distance, speed) {
        if (this.platformType === 'moving') {
            this.startX = startX;
            this.distance = distance;
            this.speed = speed;
            this.moveRight = true;
        }
        return this;
    }
    
    update() {
        if (this.platformType === 'moving') {
            if (this.moveRight) {
                this.x += this.speed;
                if (this.x >= this.startX + this.distance) {
                    this.moveRight = false;
                }
            } else {
                this.x -= this.speed;
                if (this.x <= this.startX) {
                    this.moveRight = true;
                }
            }
        }
    }
}
