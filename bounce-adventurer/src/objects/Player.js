class Player extends Phaser.Physics.Arcade.Sprite {
    constructor(scene, x, y) {
        super(scene, x, y, 'player');
        
        // 添加到场景和物理系统
        scene.add.existing(this);
        scene.physics.add.existing(this);
        
        // 设置物理属性
        this.setBounce(0.2);
        this.setCollideWorldBounds(true);
        this.body.setCircle(28, 4, 4);
        
        // 播放默认动画
        this.play('idle');
        
        // 初始化状态
        this.isLaunching = false;
        this.canBounce = true;
    }
    
    launch(angle, power) {
        // 应用力量
        this.setVelocity(
            Math.cos(angle) * power,
            Math.sin(angle) * power
        );
        
        // 播放弹跳动画
        this.play('bounce');
        
        // 设置状态
        this.isLaunching = true;
        this.canBounce = false;
        
        // 短暂延迟后重置弹跳状态
        this.scene.time.delayedCall(500, () => {
            this.canBounce = true;
        });
    }
    
    update() {
        // 检查是否静止
        if (this.body.velocity.x < 10 && this.body.velocity.x > -10 &&
            this.body.velocity.y < 10 && this.body.velocity.y > -10) {
            
            if (this.isLaunching) {
                this.isLaunching = false;
                this.play('idle', true);
            }
        }
    }
    
    collectGem() {
        // 播放收集动画
        this.play('collect');
    }
    
    hitSpike() {
        // 播放受伤效果
        this.setTint(0xff0000);
        this.scene.time.delayedCall(300, () => {
            this.clearTint();
        });
    }
}
