// 游戏主配置
const config = {
    type: Phaser.AUTO,
    width: 800,
    height: 600,
    parent: 'game-container',
    physics: {
        default: 'arcade',
        arcade: {
            gravity: { y: 300 },
            debug: false
        }
    },
    scene: [
        BootScene,
        PreloadScene,
        MenuScene,
        GameScene,
        ResultScene
    ],
    scale: {
        mode: Phaser.Scale.FIT,
        autoCenter: Phaser.Scale.CENTER_BOTH
    },
    pixelArt: false,
    roundPixels: true
};

// 创建游戏实例
window.addEventListener('load', () => {
    const game = new Phaser.Game(config);
    
    // 添加全局游戏状态
    game.gameData = {
        currentLevel: 1,
        totalLevels: 10,
        totalGems: 0,
        currentLevelGems: 0,
        bounceCount: 0,
        maxBounces: 10,
        stars: 0,
        playerSkin: 'default'
    };
    
    // 处理移动设备上的触摸事件
    window.addEventListener('touchstart', function(e) {
        if (e.touches.length > 1) {
            e.preventDefault();
        }
    }, { passive: false });
    
    window.addEventListener('touchmove', function(e) {
        if (e.touches.length > 1) {
            e.preventDefault();
        }
    }, { passive: false });
});
