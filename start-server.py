#!/usr/bin/env python3
"""
简单的HTTP服务器启动脚本
用于解决CORS问题，让游戏能够正常加载资源
"""

import http.server
import socketserver
import webbrowser
import os
import sys

# 设置端口
PORT = 8000

# 切换到bounce-adventurer目录
game_dir = os.path.join(os.path.dirname(__file__), 'bounce-adventurer')
if os.path.exists(game_dir):
    os.chdir(game_dir)
    print(f"切换到游戏目录: {game_dir}")
else:
    print("错误：找不到bounce-adventurer目录")
    sys.exit(1)

# 创建HTTP服务器
Handler = http.server.SimpleHTTPRequestHandler

try:
    with socketserver.TCPServer(("", PORT), Handler) as httpd:
        print(f"服务器启动成功！")
        print(f"本地地址: http://localhost:{PORT}")
        print(f"游戏地址: http://localhost:{PORT}/index.html")
        print("按 Ctrl+C 停止服务器")
        
        # 自动打开浏览器
        webbrowser.open(f'http://localhost:{PORT}/index.html')
        
        # 启动服务器
        httpd.serve_forever()
        
except KeyboardInterrupt:
    print("\n服务器已停止")
except OSError as e:
    if e.errno == 48:  # Address already in use
        print(f"端口 {PORT} 已被占用，请尝试其他端口")
    else:
        print(f"启动服务器时出错: {e}")
