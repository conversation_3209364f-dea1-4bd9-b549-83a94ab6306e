#!/usr/bin/env python3
"""
创建游戏所需的占位符图片
使用PIL库生成简单的彩色图片
"""

import os
from PIL import Image, ImageDraw, ImageFont

def create_solid_color_image(width, height, color, filename):
    """创建纯色图片"""
    img = Image.new('RGBA', (width, height), color)
    img.save(filename)
    return img

def create_platform_image(width, height, color, filename):
    """创建平台图片"""
    img = Image.new('RGBA', (width, height), color)
    draw = ImageDraw.Draw(img)
    
    # 添加边框
    draw.rectangle([0, 0, width-1, height-1], outline=(0, 0, 0, 255), width=2)
    
    # 添加纹理线条
    for i in range(0, width, 8):
        draw.line([i, 0, i, height], fill=(255, 255, 255, 100), width=1)
    
    img.save(filename)
    return img

def create_gem_image(size, filename):
    """创建宝石图片"""
    img = Image.new('RGBA', (size, size), (0, 0, 0, 0))
    draw = ImageDraw.Draw(img)
    
    # 绘制钻石形状
    center = size // 2
    points = [
        (center, 5),           # 顶部
        (size - 5, center),    # 右侧
        (center, size - 5),    # 底部
        (5, center)            # 左侧
    ]
    
    # 主体
    draw.polygon(points, fill=(0, 255, 255, 255))  # 青色
    
    # 高光
    highlight_points = [
        (center, 8),
        (center + 8, center - 8),
        (center, center - 2),
        (center - 8, center - 8)
    ]
    draw.polygon(highlight_points, fill=(255, 255, 255, 200))
    
    img.save(filename)
    return img

def create_player_image(width, height, filename):
    """创建玩家图片"""
    img = Image.new('RGBA', (width, height), (0, 0, 0, 0))
    draw = ImageDraw.Draw(img)
    
    # 绘制圆形玩家
    margin = 4
    draw.ellipse([margin, margin, width-margin, height-margin], 
                fill=(255, 100, 100, 255), outline=(200, 50, 50, 255), width=2)
    
    # 添加眼睛
    eye_size = 6
    eye_y = height // 3
    draw.ellipse([width//3 - eye_size//2, eye_y, width//3 + eye_size//2, eye_y + eye_size], 
                fill=(255, 255, 255, 255))
    draw.ellipse([2*width//3 - eye_size//2, eye_y, 2*width//3 + eye_size//2, eye_y + eye_size], 
                fill=(255, 255, 255, 255))
    
    # 瞳孔
    pupil_size = 3
    draw.ellipse([width//3 - pupil_size//2, eye_y + 1, width//3 + pupil_size//2, eye_y + 1 + pupil_size], 
                fill=(0, 0, 0, 255))
    draw.ellipse([2*width//3 - pupil_size//2, eye_y + 1, 2*width//3 + pupil_size//2, eye_y + 1 + pupil_size], 
                fill=(0, 0, 0, 255))
    
    img.save(filename)
    return img

def create_spike_image(width, height, filename):
    """创建尖刺图片"""
    img = Image.new('RGBA', (width, height), (0, 0, 0, 0))
    draw = ImageDraw.Draw(img)
    
    # 绘制三角形尖刺
    points = [
        (width // 2, 0),      # 顶部
        (0, height),          # 左下
        (width, height)       # 右下
    ]
    
    draw.polygon(points, fill=(255, 0, 0, 255), outline=(150, 0, 0, 255), width=2)
    
    img.save(filename)
    return img

def create_star_image(size, filled, filename):
    """创建星星图片"""
    img = Image.new('RGBA', (size, size), (0, 0, 0, 0))
    draw = ImageDraw.Draw(img)
    
    # 五角星的点
    import math
    center = size // 2
    outer_radius = size // 2 - 2
    inner_radius = outer_radius * 0.4
    
    points = []
    for i in range(10):
        angle = i * math.pi / 5 - math.pi / 2
        if i % 2 == 0:
            # 外部点
            x = center + outer_radius * math.cos(angle)
            y = center + outer_radius * math.sin(angle)
        else:
            # 内部点
            x = center + inner_radius * math.cos(angle)
            y = center + inner_radius * math.sin(angle)
        points.append((x, y))
    
    if filled:
        color = (255, 255, 0, 255)  # 黄色
    else:
        color = (128, 128, 128, 255)  # 灰色
    
    draw.polygon(points, fill=color, outline=(200, 200, 0, 255), width=1)
    
    img.save(filename)
    return img

def main():
    # 确保图片目录存在
    images_dir = 'bounce-adventurer/assets/images'
    os.makedirs(images_dir, exist_ok=True)
    
    print("正在创建占位符图片...")
    
    try:
        # 创建各种游戏图片
        images_to_create = [
            # 背景和UI
            ('loading-background.png', (800, 600, (50, 50, 100, 255), 'solid')),
            ('loading-bar.png', (300, 20, (0, 255, 0, 255), 'solid')),
            ('background.png', (800, 600, (135, 206, 235, 255), 'solid')),

            # 平台
            ('platform.png', (100, 20, (139, 69, 19, 255), 'platform')),
            ('bounce-platform.png', (100, 20, (255, 165, 0, 255), 'platform')),
            ('moving-platform.png', (100, 20, (128, 0, 128, 255), 'platform')),

            # 游戏对象
            ('gem.png', (32, 32, None, 'gem')),
            ('player.png', (32, 32, None, 'player')),
            ('spike.png', (32, 32, None, 'spike')),
            ('portal.png', (64, 64, (0, 255, 0, 200), 'solid')),
            ('wind.png', (32, 32, (173, 216, 230, 150), 'solid')),
            ('extra-bounce.png', (32, 32, (255, 215, 0, 255), 'solid')),

            # 星星
            ('star.png', (32, 32, True, 'star')),
            ('empty-star.png', (32, 32, False, 'star')),
        ]

        for filename, params in images_to_create:
            filepath = os.path.join(images_dir, filename)
            width, height, color_or_param, img_type = params

            if img_type == 'solid':
                create_solid_color_image(width, height, color_or_param, filepath)
            elif img_type == 'platform':
                create_platform_image(width, height, color_or_param, filepath)
            elif img_type == 'gem':
                create_gem_image(width, filepath)
            elif img_type == 'player':
                create_player_image(width, height, filepath)
            elif img_type == 'spike':
                create_spike_image(width, height, filepath)
            elif img_type == 'star':
                create_star_image(width, color_or_param, filepath)

            print(f"✓ 创建了 {filepath}")
        
        print("\n所有占位符图片创建完成！")
        
    except ImportError:
        print("错误：需要安装PIL库")
        print("请运行: pip install Pillow")
        return False
    
    except Exception as e:
        print(f"创建图片时出错: {e}")
        return False
    
    return True

if __name__ == "__main__":
    main()
