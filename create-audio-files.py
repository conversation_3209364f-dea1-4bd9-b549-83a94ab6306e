#!/usr/bin/env python3
"""
创建游戏所需的音频文件
生成简单的音效和背景音乐
"""

import os
import wave
import struct
import math

def create_sine_wave(frequency, duration, sample_rate=44100, amplitude=0.3):
    """创建正弦波音频数据"""
    frames = int(duration * sample_rate)
    audio_data = []
    
    for i in range(frames):
        # 计算正弦波值
        value = amplitude * math.sin(2 * math.pi * frequency * i / sample_rate)
        # 转换为16位整数
        audio_data.append(int(value * 32767))
    
    return audio_data

def create_bounce_sound():
    """创建弹跳音效 - 快速下降的音调"""
    sample_rate = 44100
    duration = 0.3
    frames = int(duration * sample_rate)
    audio_data = []
    
    for i in range(frames):
        # 频率从800Hz下降到200Hz
        frequency = 800 - (600 * i / frames)
        # 音量逐渐减小
        amplitude = 0.5 * (1 - i / frames)
        value = amplitude * math.sin(2 * math.pi * frequency * i / sample_rate)
        audio_data.append(int(value * 32767))
    
    return audio_data

def create_collect_sound():
    """创建收集音效 - 上升的音调"""
    sample_rate = 44100
    duration = 0.4
    frames = int(duration * sample_rate)
    audio_data = []
    
    for i in range(frames):
        # 频率从400Hz上升到800Hz
        frequency = 400 + (400 * i / frames)
        # 音量逐渐减小
        amplitude = 0.4 * (1 - i / frames)
        value = amplitude * math.sin(2 * math.pi * frequency * i / sample_rate)
        audio_data.append(int(value * 32767))
    
    return audio_data

def create_success_sound():
    """创建成功音效 - 和弦"""
    sample_rate = 44100
    duration = 1.0
    frames = int(duration * sample_rate)
    audio_data = []
    
    # C大调和弦 (C-E-G)
    frequencies = [523.25, 659.25, 783.99]  # C5, E5, G5
    
    for i in range(frames):
        value = 0
        amplitude = 0.3 * (1 - i / frames)  # 逐渐减小
        
        for freq in frequencies:
            value += amplitude * math.sin(2 * math.pi * freq * i / sample_rate)
        
        audio_data.append(int(value * 32767 / len(frequencies)))
    
    return audio_data

def create_fail_sound():
    """创建失败音效 - 下降的不和谐音"""
    sample_rate = 44100
    duration = 0.8
    frames = int(duration * sample_rate)
    audio_data = []
    
    for i in range(frames):
        # 两个不和谐的频率
        freq1 = 300 - (100 * i / frames)
        freq2 = 250 - (80 * i / frames)
        amplitude = 0.3 * (1 - i / frames)
        
        value1 = amplitude * math.sin(2 * math.pi * freq1 * i / sample_rate)
        value2 = amplitude * math.sin(2 * math.pi * freq2 * i / sample_rate)
        
        audio_data.append(int((value1 + value2) * 32767 / 2))
    
    return audio_data

def create_background_music():
    """创建简单的背景音乐 - 循环的旋律"""
    sample_rate = 44100
    duration = 8.0  # 8秒循环
    frames = int(duration * sample_rate)
    audio_data = []
    
    # 简单的旋律音符 (C大调音阶)
    notes = [523.25, 587.33, 659.25, 698.46, 783.99, 880.00, 987.77, 1046.50]  # C5到C6
    note_duration = duration / len(notes)
    
    for note_idx, frequency in enumerate(notes):
        note_frames = int(note_duration * sample_rate)
        
        for i in range(note_frames):
            # 添加一些和声
            value = 0.2 * math.sin(2 * math.pi * frequency * i / sample_rate)
            value += 0.1 * math.sin(2 * math.pi * frequency * 2 * i / sample_rate)  # 八度
            
            # 音符包络（渐入渐出）
            envelope = 1.0
            if i < note_frames * 0.1:  # 渐入
                envelope = i / (note_frames * 0.1)
            elif i > note_frames * 0.9:  # 渐出
                envelope = (note_frames - i) / (note_frames * 0.1)
            
            audio_data.append(int(value * envelope * 32767))
    
    return audio_data

def save_wav_file(filename, audio_data, sample_rate=44100):
    """保存音频数据为WAV文件"""
    with wave.open(filename, 'w') as wav_file:
        wav_file.setnchannels(1)  # 单声道
        wav_file.setsampwidth(2)  # 16位
        wav_file.setframerate(sample_rate)
        
        # 将音频数据转换为字节
        wav_data = b''.join(struct.pack('<h', sample) for sample in audio_data)
        wav_file.writeframes(wav_data)

def main():
    # 确保音频目录存在
    audio_dir = 'bounce-adventurer/assets/audio'
    os.makedirs(audio_dir, exist_ok=True)
    
    print("正在创建音频文件...")
    
    # 创建各种音效
    audio_files = {
        'bounce.wav': create_bounce_sound(),
        'collect.wav': create_collect_sound(),
        'success.wav': create_success_sound(),
        'fail.wav': create_fail_sound(),
        'background-music.wav': create_background_music()
    }
    
    for filename, audio_data in audio_files.items():
        filepath = os.path.join(audio_dir, filename)
        save_wav_file(filepath, audio_data)
        print(f"✓ 创建了 {filepath}")
    
    print("\n所有音频文件创建完成！")
    print("注意：游戏代码中使用的是.mp3文件，您可能需要:")
    print("1. 将这些.wav文件转换为.mp3格式")
    print("2. 或者修改游戏代码使用.wav文件")

if __name__ == "__main__":
    main()
